name: Project Board Automation

on:
  issues:
    types: [opened, closed, labeled, unlabeled, assigned]
  pull_request:
    types: [opened, closed, merged]

jobs:
  update-project:
    runs-on: ubuntu-latest
    steps:
      - name: Add Issue to Project
        if: github.event.action == 'opened' && github.event.issue
        uses: actions/add-to-project@v0.4.0
        with:
          project-url: https://github.com/users/Amoresdk/projects/1  # 替换为实际项目URL
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Update Project Status Based on Labels
        if: github.event.action == 'labeled'
        uses: actions/github-script@v6
        with:
          script: |
            const issue = context.payload.issue;
            const labels = issue.labels.map(label => label.name);
            
            // 根据Sprint标签设置状态
            if (labels.includes('sprint:1')) {
              // 使用GraphQL API更新项目状态
              console.log('Moving to Sprint 1 column');
            } else if (labels.includes('sprint:2')) {
              console.log('Moving to Sprint 2 column');
            } else if (labels.includes('sprint:3')) {
              console.log('Moving to Sprint 3 column');
            } else if (labels.includes('sprint:4')) {
              console.log('Moving to Sprint 4 column');
            }
            
            // 根据优先级标签排序
            if (labels.includes('priority:high')) {
              console.log('Setting high priority');
            }

      - name: Move to Done on Issue Close
        if: github.event.action == 'closed' && github.event.issue
        uses: actions/github-script@v6
        with:
          script: |
            console.log('Moving closed issue to Done column');
            // 实现移动到Done列的逻辑

      - name: Update Sprint Progress
        uses: actions/github-script@v6
        with:
          script: |
            // 计算Sprint进度
            const { data: issues } = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'all',
              labels: 'sprint:1'
            });
            
            const total = issues.length;
            const completed = issues.filter(issue => issue.state === 'closed').length;
            const progress = Math.round((completed / total) * 100);
            
            console.log(`Sprint 1 Progress: ${progress}% (${completed}/${total})`);
